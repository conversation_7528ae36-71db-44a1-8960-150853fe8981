"""
Sales Script model for database operations
"""
import uuid
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, func
from sqlalchemy.dialects.postgresql import UUID
from app.core.database.connection import Base


class SalesScript(Base):
    """Sales script model for storing sales conversation scripts"""
    
    __tablename__ = "sales_script"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    script_title = Column(String(100), nullable=False, unique=True, index=True)
    script_content = Column(Text, nullable=False)
    script_stage = Column(String(50), nullable=False, index=True)
    order_sequence = Column(Integer, nullable=False, default=1)
    has_variables = Column(Boolean, nullable=False, default=False)
    variable_schema = Column(Text, nullable=True)
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    is_deleted = Column(Boolean, nullable=False, default=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<SalesScript(id={self.id}, title={self.script_title}, stage={self.script_stage}, is_active={self.is_active})>"
