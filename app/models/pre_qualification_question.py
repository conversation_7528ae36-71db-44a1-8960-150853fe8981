"""
Pre-qualification Question Model
SQLAlchemy model for storing pre-qualification questions used in lead qualification
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4
from sqlalchemy import String, Boolean, DateTime, Text, Integer, Float, func, JSON
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column
from app.core.database.connection import Base


class PreQualificationQuestion(Base):
    """Pre-qualification question model for lead qualification process"""

    __tablename__ = "pre_qualification_questions"
    __table_args__ = {'extend_existing': True}

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PGUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        server_default=func.gen_random_uuid(),
    )

    # Foreign key to franchisor
    franchisor_id: Mapped[Optional[UUID]] = mapped_column(
        PGUUID(as_uuid=True),
        nullable=True,
        index=True,
        comment="ID of the franchisor this question belongs to",
    )

    # Question details
    question_text: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="The question text to be asked to the lead",
    )

    question_internal_text: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Internal text for the question (for admin use)",
    )

    question_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        comment="Type of question (text, multiple_choice, yes_no, numeric, budget)",
    )
    
    order_sequence: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="Order in which questions should be asked",
    )
    
    # Qualification scoring
    qualification_weight: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="Weight of this question in qualification scoring (0.0 to 1.0)",
    )
    
    passing_criteria: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON criteria for what constitutes a qualifying answer",
    )
    
    # Answer validation
    expected_answer_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="text",
        comment="Expected answer type (text, number, boolean, choice)",
    )
    
    validation_rules: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON validation rules for the answer",
    )

    expected_answers: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="JSON array of expected answers",
    )
    
    # Multiple choice options
    answer_options: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON array of answer options for multiple choice questions",
    )
    
    # Follow-up behavior
    requires_follow_up: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Whether this question requires a follow-up based on the answer",
    )
    
    follow_up_logic: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="JSON logic for follow-up questions based on answers",
    )

    # Status fields
    is_required: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="Whether this question is required in the qualification process",
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this question is active and should be used",
    )
    
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        index=True,
        comment="Soft delete flag",
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Creation timestamp",
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Last update timestamp",
    )
    
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="Deletion timestamp",
    )

    # Relationships
    # Note: LeadResponse relationship removed to simplify questions module update

    def __repr__(self) -> str:
        return (
            f"<PreQualificationQuestion(id={self.id}, type={self.question_type}, "
            f"sequence={self.order_sequence}, active={self.is_active})>"
        )
