"""
Zoho Data Mapper
Maps data between CMS format and Zoho CRM format
"""

from typing import Dict, Optional, Any
from datetime import datetime, timezone
from app.core.logging import logger


class ZohoDataMapper:
    """Maps data between CMS and Zoho CRM formats"""
    
    @staticmethod
    def cms_lead_to_zoho(cms_lead: Dict) -> Dict:
        """Convert CMS lead format to Zoho CRM format"""
        try:
            zoho_lead = {
                # Name fields
                "First_Name": cms_lead.get("first_name", ""),
                "Last_Name": cms_lead.get("last_name", ""),

                # Contact information
                "Email": cms_lead.get("email"),
                "Phone": cms_lead.get("phone"),
                "Mobile": cms_lead.get("mobile"),
                "City": cms_lead.get("location"),
                "Zip_Code": cms_lead.get("postal_code"),

                # Reference fields
                "Lead_Source": cms_lead.get("lead_source", "Website"),
                "Lead_Status": ZohoDataMapper._map_cms_lead_status_to_zoho(cms_lead.get("status", "new")),
                "Franchisor": cms_lead.get("franchisor_name"),

                # Budget preference
                "Funds_to_invest": ZohoDataMapper._convert_to_json_array(cms_lead.get("funds_to_invest")),

                # Franchise buyer information fields (some may need JSON array format)
                "Franchise_interested_in": ZohoDataMapper._convert_to_json_array(cms_lead.get("franchise_interested_in")),
                "Looking_for_business_opportunity_since": cms_lead.get("looking_for_business_opportunity_since"),
                "Skills": cms_lead.get("skills"),
                "Looking_to_be_an_Owner_Operator": cms_lead.get("looking_to_be_owner_operator"),
                "When_are_you_looking_to_start": cms_lead.get("when_looking_to_start"),
                "Ethnic_Background": cms_lead.get("ethnic_background"),
                "EOI_NDA_Link": cms_lead.get("eoi_nda_link"),
                "Work_Background": cms_lead.get("work_background"),
                "Motivation_to_Enquire": ZohoDataMapper._convert_to_json_array(cms_lead.get("motivation_to_enquire")),
                "Funds_Available": ZohoDataMapper._convert_to_integer(cms_lead.get("funds_available")),
                "Motivation": cms_lead.get("motivation"),

                # Boolean fields (convert to Yes/No)
                "Have_you_run_business_before": "Yes" if cms_lead.get("have_run_business_before") else "No",
                "Do_you_have_a_mortgage": "Yes" if cms_lead.get("have_mortgage") else "No",
                "High_Net_Worth": cms_lead.get("high_net_worth"),

                # System fields
                "Created_Time": ZohoDataMapper._serialize_datetime(cms_lead.get("created_at")),
                "Modified_Time": ZohoDataMapper._serialize_datetime(cms_lead.get("updated_at"))
            }

            # Add Zoho ID if updating existing lead
            if cms_lead.get("zoho_lead_id"):
                zoho_lead["id"] = cms_lead["zoho_lead_id"]

            # Remove None values and empty strings
            zoho_lead = {k: v for k, v in zoho_lead.items() if v is not None and v != ""}

            return zoho_lead
            
        except Exception as e:
            logger.error(f"Error mapping CMS lead to Zoho format: {e}")
            raise
    
    @staticmethod
    def zoho_lead_to_cms(zoho_lead: Dict) -> Dict:
        """Convert Zoho CRM lead format to CMS format"""
        try:
            print("Zoho lead data: ", zoho_lead)

            cms_lead = {
                # Name fields (split from Full_Name)
                "first_name": zoho_lead.get("First_Name", ""),
                "last_name": zoho_lead.get("Last_Name", ""),

                # Contact information
                "email": zoho_lead.get("Email"),
                "phone": zoho_lead.get("Phone"),
                "mobile": zoho_lead.get("Mobile"),
                "location": zoho_lead.get("City"),
                "postal_code": zoho_lead.get("Zip_Code") or zoho_lead.get("Postcode"),

                # Reference fields (will be converted to IDs in sync service)
                "lead_source": zoho_lead.get("Lead_Source", "Zoho"),
                "status": ZohoDataMapper._map_zoho_lead_status_to_cms(zoho_lead.get("Lead_Status", "")),
                "franchisor_name": zoho_lead.get("Franchisor"),

                # Store the Zoho Franchisor ID (will be used to link to franchisor by franchisor_won_id)
                "franchisor_won_id": zoho_lead.get("Franchisor_Id"),

                # Budget preference - handle arrays and convert to string (now that column is VARCHAR)
                "budget_preference": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Funds_to_invest")),

                # Franchise buyer information fields - handle arrays
                "franchise_interested_in": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Franchise_interested_in")),
                "looking_for_business_opportunity_since": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Looking_for_business_opportunity_since")),
                "skills": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Skills")),
                "looking_to_be_owner_operator": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Looking_to_be_an_Owner_Operator")),
                "when_looking_to_start": ZohoDataMapper._convert_array_to_string(zoho_lead.get("When_are_you_looking_to_start")),
                "ethnic_background": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Ethnic_Background")),
                "funds_to_invest": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Funds_to_invest")),
                "eoi_nda_link": ZohoDataMapper._convert_array_to_string(zoho_lead.get("EOI_NDA_Link")),
                "work_background": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Work_Background")),
                "motivation_to_enquire": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Motivation_to_Enquire")),
                "funds_available": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Funds_Available")),
                "motivation": ZohoDataMapper._convert_array_to_string(zoho_lead.get("Motivation")),

                # Boolean fields
                "have_run_business_before": zoho_lead.get("Have_you_run_business_before") == "Yes",
                "have_mortgage": zoho_lead.get("Do_you_have_a_mortgage") == "Yes",
                "high_net_worth": zoho_lead.get("High_Net_Worth"),

                # System fields
                "zoho_lead_id": zoho_lead.get("id"),
                # Note: created_at and updated_at will be parsed below, not set as raw strings
            }

            # Parse Zoho timestamps for both database storage and conflict resolution
            if zoho_lead.get("Created_Time"):
                try:
                    raw_created_time = zoho_lead["Created_Time"]
                    logger.info(f"DEBUG: Parsing Zoho Created_Time: '{raw_created_time}'")
                    parsed_created_time = ZohoDataMapper._parse_zoho_datetime(raw_created_time)
                    logger.info(f"DEBUG: Parsed to: {parsed_created_time}")
                    cms_lead["created_at"] = parsed_created_time  # For database storage
                    cms_lead["zoho_created_at"] = parsed_created_time  # For conflict resolution
                except Exception as e:
                    logger.warning(f"Failed to parse Created_Time '{zoho_lead.get('Created_Time')}': {e}")

            if zoho_lead.get("Modified_Time"):
                try:
                    raw_modified_time = zoho_lead["Modified_Time"]
                    logger.info(f"DEBUG: Parsing Zoho Modified_Time: '{raw_modified_time}'")
                    parsed_modified_time = ZohoDataMapper._parse_zoho_datetime(raw_modified_time)
                    logger.info(f"DEBUG: Parsed to: {parsed_modified_time}")
                    cms_lead["updated_at"] = parsed_modified_time  # For database storage
                    # Note: zoho_modified_at field doesn't exist in database, so we don't set it
                except Exception as e:
                    logger.warning(f"Failed to parse Modified_Time '{zoho_lead.get('Modified_Time')}': {e}")
                    # Set current time as fallback for database storage
                    from datetime import datetime
                    cms_lead["updated_at"] = datetime.now(datetime.timezone.utc)

            # Remove None values, empty strings, and empty arrays
            cms_lead = {k: v for k, v in cms_lead.items() if v is not None and v != "" and v != []}

            return cms_lead
            
        except Exception as e:
            logger.error(f"Error mapping Zoho lead to CMS format: {e}")
            raise
    
    @staticmethod
    def _map_cms_lead_status_to_zoho(cms_status: str) -> str:
        """Map CMS lead status to Zoho lead status"""
        status_mapping = {
            "new": "New Lead",
            "contacted": "1st Call - No Answer",
            "qualified": "Qualified",
            "unqualified": "Not Qualified",
            "not_interested": "Not Interested",
            "follow_up": "Follow up Required",
            "callback": "Call Back",
            "junk": "Junk Lead",
            "wrong_number": "Wrong Number",
            "out_of_budget": "Out of Budget",
            "region_not_available": "Region is not available",
            "eoi_sent": "EOI/NDA Sent",
            "eoi_signed": "EOI/NDA Signed",
            "application_signed": "Application Form Signed",
            "deposit_paid": "Deposit Paid",
            "franchise_sold": "Franchise Sold",
            "lost": "Not Interested",
            "converted": "Franchise Sold"
        }
        return status_mapping.get(cms_status.lower() if cms_status else "", "New Lead")
    
    @staticmethod
    def _map_zoho_lead_status_to_cms(zoho_status: str) -> str:
        """Map Zoho lead status to CMS lead status"""
        status_mapping = {
            "new lead": "new",
            "1st call - no answer": "contacted",
            "2nd call - no answer": "contacted",
            "3rd call - no answer": "contacted",
            "qualified": "qualified",
            "not qualified": "unqualified",
            "not interested": "not_interested",
            "follow up required": "follow_up",
            "call back": "callback",
            "junk lead": "junk",
            "wrong number": "wrong_number",
            "out of budget": "out_of_budget",
            "region is not available": "region_not_available",
            "eoi/nda sent": "eoi_sent",
            "eoi/nda signed": "eoi_signed",
            "application form signed": "application_signed",
            "deposit paid": "deposit_paid",
            "franchise sold": "franchise_sold",
            "franchise database": "qualified"
        }
        return status_mapping.get(zoho_status.lower() if zoho_status else "", "new")

    @staticmethod
    def _map_cms_franchisor_status_to_zoho(cms_status: str) -> str:
        """Map CMS franchisor status to Zoho franchisor status"""
        status_mapping = {
            "new": "On the radar - 0%",
            "radar": "On the raddar - 0",
            "intro_call": "Introductory Call - 10%",
            "discovery_call": "Discovery Call",
            "pitch": "PItch/ Proposal - 25%",
            "following_presentation": "Following Up on Presentation",
            "following_up": "Following Up - 35%",
            "negotiation": "Negotiation - 65%",
            "agreement_sent": "Agreement Sent - 75%",
            "agreement_signed": "Agreement Signed",
            "invoice_sent": "Invoice Sent",
            "won": "Sale Won - 100%",
            "lost": "Sale - Lost",
            "diary_followup": "Not this time - in Diary to follow up"
        }
        return status_mapping.get(cms_status.lower() if cms_status else "", "On the radar - 0%")

    @staticmethod
    def _map_zoho_franchisor_status_to_cms(zoho_status: str) -> str:
        """Map Zoho franchisor status to CMS franchisor status"""
        status_mapping = {
            "on the radar - 0%": "radar",
            "on the raddar - 0": "radar",
            "introductory call - 10%": "intro_call",
            "discovery call": "discovery_call",
            "pitch/ proposal - 25%": "pitch",
            "following up on presentation": "following_presentation",
            "following up - 35%": "following_up",
            "negotiation - 65%": "negotiation",
            "agreement sent - 75%": "agreement_sent",
            "agreement signed": "agreement_signed",
            "invoice sent": "invoice_sent",
            "sale won - 100%": "won",
            "sale - lost": "lost",
            "not this time - in diary to follow up": "diary_followup"
        }
        return status_mapping.get(zoho_status.lower() if zoho_status else "", "new")
    
    @staticmethod
    def _parse_zoho_datetime(zoho_datetime: str) -> Optional[datetime]:
        """Parse Zoho datetime string to Python datetime with timezone awareness"""
        try:
            # Zoho typically returns datetime in ISO format
            # Example: "2024-01-15T10:30:00+05:30"
            if zoho_datetime:
                # Handle different timezone formats
                if 'Z' in zoho_datetime:
                    # Replace Z with +00:00 for UTC
                    zoho_datetime = zoho_datetime.replace('Z', '+00:00')

                # Parse with timezone info preserved
                dt = datetime.fromisoformat(zoho_datetime)

                # Convert to UTC if it has timezone info
                if dt.tzinfo is not None:
                    dt = dt.astimezone(timezone.utc)
                else:
                    # If no timezone info, assume UTC
                    dt = dt.replace(tzinfo=timezone.utc)

                return dt
        except Exception as e:
            logger.warning(f"Could not parse Zoho datetime '{zoho_datetime}': {e}")

        return None

    @staticmethod
    def _serialize_datetime(dt) -> Optional[str]:
        """Convert datetime object to ISO string for JSON serialization"""
        if dt is None:
            return None
        if isinstance(dt, datetime):
            return dt.isoformat()
        if isinstance(dt, str):
            return dt
        return None

    @staticmethod
    def cms_franchisor_to_zoho(cms_franchisor: Dict) -> Dict:
        """Convert CMS franchisor format to Zoho CRM format"""
        try:
            zoho_franchisor = {
                "Name": cms_franchisor.get("name", ""),
                "Contact_First_Name": cms_franchisor.get("contactfirstname") or cms_franchisor.get("contactFirstName"),
                "Contact_Last_Name": cms_franchisor.get("contactlastname") or cms_franchisor.get("contactLastName"),
                "Email": cms_franchisor.get("email"),
                "Mobile": cms_franchisor.get("phone"),
                "Billing_State": cms_franchisor.get("region"),
                "Created_Time": ZohoDataMapper._serialize_datetime(cms_franchisor.get("created_at")),
                "Modified_Time": ZohoDataMapper._serialize_datetime(cms_franchisor.get("updated_at")),
                "Type": "Franchisor",  # Default type for franchisors
            }

            # Map region if available
            if cms_franchisor.get("region"):
                zoho_franchisor["Region"] = cms_franchisor["region"]

            # Map budget if available
            if cms_franchisor.get("budget"):
                zoho_franchisor["Budget"] = float(cms_franchisor["budget"])

            # Map industry if available
            if cms_franchisor.get("industry"):
                zoho_franchisor["Industry"] = cms_franchisor["industry"]

            # Map status if available
            if cms_franchisor.get("status"):
                zoho_franchisor["Sales_Stage"] = ZohoDataMapper._map_cms_franchisor_status_to_zoho(cms_franchisor["status"])

            # Add Zoho ID if updating existing franchisor
            if cms_franchisor.get("zoho_franchisor_id"):
                zoho_franchisor["id"] = cms_franchisor["zoho_franchisor_id"]

            # Remove None values
            zoho_franchisor = {k: v for k, v in zoho_franchisor.items() if v is not None and v != ""}

            return zoho_franchisor

        except Exception as e:
            logger.error(f"Error mapping CMS franchisor to Zoho format: {e}")
            raise

    @staticmethod
    def zoho_franchisor_to_cms(zoho_franchisor: Dict) -> Dict:
        """Convert Zoho CRM franchisor format to CMS format"""
        try:
            cms_franchisor = {
                "name": zoho_franchisor.get("Name", ""),
                "contactfirstname": zoho_franchisor.get("Contact_First_Name"),
                "contactlastname": zoho_franchisor.get("Contact_Last_Name"),
                "email": zoho_franchisor.get("Email"),
                "phone": zoho_franchisor.get("Mobile"),
                "region": zoho_franchisor.get("Billing_State"),
                "zoho_franchisor_id": zoho_franchisor.get("id"),
                "franchisor_won_id": zoho_franchisor.get("Franchisor_Won_ID"),  # New field
                "is_active": True,
            }

            # Map budget if available
            if zoho_franchisor.get("Budget"):
                try:
                    cms_franchisor["budget"] = float(zoho_franchisor["Budget"])
                except (ValueError, TypeError):
                    pass

            # Note: Franchisor model doesn't have status field, so we skip status mapping

            # Parse Zoho timestamps to datetime objects
            if zoho_franchisor.get("Created_Time"):
                try:
                    cms_franchisor["created_at"] = ZohoDataMapper._parse_zoho_datetime(zoho_franchisor["Created_Time"])
                except Exception as e:
                    logger.warning(f"Failed to parse Created_Time: {e}")
                    cms_franchisor["created_at"] = datetime.now(datetime.timezone.utc)

            if zoho_franchisor.get("Modified_Time"):
                try:
                    raw_modified_time = zoho_franchisor["Modified_Time"]
                    logger.info(f"DEBUG: Parsing Zoho Modified_Time: '{raw_modified_time}'")
                    parsed_modified_time = ZohoDataMapper._parse_zoho_datetime(raw_modified_time)
                    logger.info(f"DEBUG: Parsed to: {parsed_modified_time}")
                    cms_franchisor["updated_at"] = parsed_modified_time
                    # Note: zoho_modified_at field doesn't exist in database, so we don't set it
                except Exception as e:
                    logger.warning(f"Failed to parse Modified_Time '{zoho_franchisor.get('Modified_Time')}': {e}")
                    cms_franchisor["updated_at"] = datetime.now(datetime.timezone.utc)

            # Remove None values
            cms_franchisor = {k: v for k, v in cms_franchisor.items() if v is not None and v != ""}

            return cms_franchisor

        except Exception as e:
            logger.error(f"Error mapping Zoho franchisor to CMS format: {e}")
            raise

    @staticmethod
    def prepare_sync_data(entity_data: Dict, operation: str) -> Dict:
        """Prepare data for sync logging"""
        return {
            "operation": operation,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": entity_data
        }

    @staticmethod
    def _convert_array_to_string(value):
        """Convert array values to strings, handle empty arrays and None values"""
        if value is None:
            return None
        if isinstance(value, list):
            if len(value) == 0:
                return None
            # Join array elements with comma if multiple values
            return ", ".join(str(item) for item in value if item is not None and str(item).strip())
        return str(value) if value else None

    @staticmethod
    def _convert_to_integer(value):
        """Convert value to integer for Zoho API, handle None and invalid values"""
        if value is None:
            return None

        try:
            # Handle string values that might contain numbers
            if isinstance(value, str):
                # Remove any non-numeric characters except decimal point
                import re
                cleaned_value = re.sub(r'[^\d.]', '', value.strip())
                if not cleaned_value:
                    return None
                # Convert to float first, then to int to handle decimal strings
                return int(float(cleaned_value))

            # Handle numeric values
            if isinstance(value, (int, float)):
                return int(value)

            # Handle list/array values (take first numeric value)
            if isinstance(value, list) and len(value) > 0:
                return ZohoDataMapper._convert_to_integer(value[0])

            return None

        except (ValueError, TypeError) as e:
            logger.warning(f"Failed to convert '{value}' to integer: {e}")
            return None

    @staticmethod
    def _convert_to_json_array(value):
        """Convert value to JSON array format for Zoho API"""
        if value is None:
            return None

        try:
            # If it's already a list, return it as is
            if isinstance(value, list):
                return value if len(value) > 0 else None

            # If it's a string, split by comma and create array
            if isinstance(value, str):
                value = value.strip()
                if not value:
                    return None
                # Split by comma and clean up each item
                items = [item.strip() for item in value.split(',') if item.strip()]
                return items if len(items) > 0 else None

            # For other types, convert to string and wrap in array
            return [str(value)] if value else None

        except Exception as e:
            logger.warning(f"Failed to convert '{value}' to JSON array: {e}")
            return None


