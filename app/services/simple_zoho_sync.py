"""
Simple Zoho Sync Service
Focused implementation for your exact requirements:
1. Sync button in franchisors module
2. Pull new/updated data from Zoho
3. Push new/updated data to CMS
4. Handle conflicts by latest timestamp
5. Only sync existing DB fields
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_, or_
from sqlalchemy.orm import selectinload

from app.services.zoho_client import zoho_client
from app.services.zoho_mapper import ZohoDataMapper
from app.models.lead import Lead
from app.models.franchisor import Franchisor
from app.core.logging import logger


class SimpleZohoSync:
    """Simple Zoho sync focused on your requirements"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def sync_with_zoho(self) -> Dict[str, any]:
        """
        Main sync function called by the "Sync with Zoho" button
        """
        try:
            logger.info("Starting Zoho sync from button click")

            # Get fresh access token using the refresh API
            try:
                access_token = await zoho_client.get_access_token()
                logger.info("Successfully obtained access token for sync")
            except Exception as e:
                logger.error(f"Failed to get access token: {e}")
                return {
                    "success": False,
                    "message": f"Authentication failed: {e}",
                    "data": {"errors": [str(e)]}
                }

            results = {
                "franchisors_pulled": 0,
                "franchisors_pushed": 0,
                "franchisors_updated": 0,
                "leads_pulled": 0,
                "leads_pushed": 0,
                "leads_updated": 0,
                "conflicts_resolved": 0,
                "errors": []
            }

            # Step 1: Pull franchisors from Zoho (only pull, no push for franchisors)
            await self._pull_franchisors_from_zoho(results)

            # Step 2: Pull leads from Zoho
            await self._pull_leads_from_zoho(results)

            # Step 3: Push new CMS leads to Zoho
            await self._push_leads_to_zoho(results)

            # Step 4: Push updated CMS leads to Zoho
            await self._push_updated_leads_to_zoho(results)
            
            # Commit all changes
            await self.db.commit()

            # DEBUG: After commit, verify what was actually saved for leads
            logger.info("DEBUG: Verifying postal codes after commit...")
            from sqlalchemy import select
            from app.models.lead import Lead

            # Check the last few leads to see their postal codes
            stmt = select(Lead).order_by(Lead.created_at.desc()).limit(3)
            result = await self.db.execute(stmt)
            recent_leads = result.scalars().all()

            for lead in recent_leads:
                logger.info(f"DEBUG: Lead {lead.first_name} {lead.last_name} - postal_code: '{lead.postal_code}'")

            return {
                "success": True,
                "message": "Sync completed successfully",
                "data": results,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            await self.db.rollback()
            return {"success": False, "message": f"Sync failed: {e}"}
    
    async def _pull_leads_from_zoho(self, results: Dict) -> None:
        """Pull new and updated leads from Zoho - only those linked to franchisors with 'Sale Won - 100%' status"""
        try:
            # Get all leads from Zoho
            all_zoho_leads = await zoho_client.get_leads()
            logger.info(f"Retrieved {len(all_zoho_leads)} total leads from Zoho")

            # DEBUG: Log first few leads
            logger.info("DEBUG: First few leads from Zoho:")
            for i, lead in enumerate(all_zoho_leads[:5]):  # Log first 5
                name = f"{lead.get('First_Name', '')} {lead.get('Last_Name', '')}".strip()
                email = lead.get("Email", "N/A")
                franchisor_id = lead.get("Franchisor_Id", "N/A")
                modified_time = lead.get("Modified_Time", "N/A")
                logger.info(f"  {i+1}. {name} | Email: {email} | Franchisor_Id: {franchisor_id} | Modified: {modified_time}")

            # Get all franchisors with "Sale Won - 100%" status to get their franchisor_won_ids
            all_zoho_franchisors = await zoho_client.get_franchisor()
            winning_franchisor_ids = set()
            for franchisor in all_zoho_franchisors:
                if franchisor.get("Sales_Stage") and franchisor.get("Sales_Stage", "").lower() == "sale won - 100%":
                    franchisor_won_id = franchisor.get("Franchisor_Won_ID")
                    if franchisor_won_id:
                        winning_franchisor_ids.add(franchisor_won_id)

            logger.info(f"Found {len(winning_franchisor_ids)} winning franchisor IDs: {winning_franchisor_ids}")

            # Filter leads to only include those linked to winning franchisors
            zoho_leads = []
            for lead in all_zoho_leads:
                franchisor_id = lead.get("Franchisor_Id")
                if franchisor_id and franchisor_id in winning_franchisor_ids:
                    zoho_leads.append(lead)
                elif not franchisor_id:
                    # Include leads without franchisor_id (they might be general inquiries)
                    zoho_leads.append(lead)

            logger.info(f"Filtered to {len(zoho_leads)} leads linked to winning franchisors (or without franchisor link)")
            
            for zoho_lead in zoho_leads:
                zoho_id = zoho_lead.get("id")
                if not zoho_id:
                    continue
                
                # Check if lead exists in CMS
                stmt = select(Lead).where(Lead.zoho_lead_id == zoho_id)
                result = await self.db.execute(stmt)
                existing_lead = result.scalar_one_or_none()
                
                if existing_lead:
                    # DEBUG: Log lead conflict resolution
                    lead_name = f"{existing_lead.first_name} {existing_lead.last_name}".strip()
                    logger.info(f"DEBUG: Found existing CMS lead: {lead_name}")
                    logger.info(f"  CMS updated_at: {existing_lead.updated_at}")
                    logger.info(f"  Zoho Modified_Time: {zoho_lead.get('Modified_Time', 'N/A')}")

                    # Handle conflict: Update if Zoho data is newer
                    if await self._should_update_from_zoho(existing_lead, zoho_lead):
                        logger.info(f"DEBUG: Zoho version is newer, updating lead")
                        await self._update_lead_from_zoho(existing_lead, zoho_lead)
                        results["conflicts_resolved"] += 1
                        logger.info(f"✅ Updated lead {existing_lead.id} from Zoho (conflict resolved)")
                    else:
                        logger.info(f"DEBUG: Lead {existing_lead.id} already up to date (Zoho not newer)")
                else:
                    # Create new lead from Zoho
                    await self._create_lead_from_zoho(zoho_lead)
                    results["leads_pulled"] += 1
                    logger.info(f"Created new lead from Zoho ID {zoho_id}")
                    
        except Exception as e:
            logger.error(f"Failed to pull leads from Zoho: {e}")
            results["errors"].append(f"Pull failed: {e}")
    
    async def _push_leads_to_zoho(self, results: Dict) -> None:
        """Push new CMS leads to Zoho using batch operations"""
        try:
            # Get CMS leads without Zoho ID (newly created in CMS) with relationships
            stmt = select(Lead).options(
                selectinload(Lead.lead_source_rel),
                selectinload(Lead.lead_status_rel)
            ).where(Lead.zoho_lead_id.is_(None))
            result = await self.db.execute(stmt)
            cms_leads = result.scalars().all()

            if not cms_leads:
                logger.info("No new leads to push to Zoho")
                return

            logger.info(f"Found {len(cms_leads)} CMS leads to push to Zoho")

            # Prepare batch data
            batch_data = []
            lead_mapping = {}  # To map batch results back to CMS leads

            for cms_lead in cms_leads:
                # Get lead source and status names for mapping
                lead_source_name = None
                if cms_lead.lead_source_rel:
                    lead_source_name = cms_lead.lead_source_rel.name

                lead_status_name = None
                if cms_lead.lead_status_rel:
                    lead_status_name = cms_lead.lead_status_rel.name

                # Get franchisor name if brand_preference is set
                franchisor_name = None
                if cms_lead.brand_preference:
                    franchisor_name = await self._get_franchisor_name_by_id(cms_lead.brand_preference)

                # Convert Lead model to dict for mapper with all fields
                cms_lead_dict = {
                    # Name fields
                    "first_name": cms_lead.first_name,
                    "last_name": cms_lead.last_name,

                    # Contact information
                    "email": cms_lead.email,
                    "phone": cms_lead.phone,
                    "mobile": cms_lead.mobile,
                    "location": cms_lead.location,
                    "postal_code": getattr(cms_lead, 'postal_code', None),

                    # Reference fields
                    "lead_source": lead_source_name,
                    "status": lead_status_name,
                    "franchisor_name": franchisor_name,

                    # Budget and franchise info
                    "budget_preference": getattr(cms_lead, 'budget_preference', None),
                    "funds_to_invest": getattr(cms_lead, 'funds_to_invest', None),

                    # Franchise buyer information fields
                    "franchise_interested_in": getattr(cms_lead, 'franchise_interested_in', None),
                    "looking_for_business_opportunity_since": getattr(cms_lead, 'looking_for_business_opportunity_since', None),
                    "skills": getattr(cms_lead, 'skills', None),
                    "looking_to_be_owner_operator": getattr(cms_lead, 'looking_to_be_owner_operator', None),
                    "when_looking_to_start": getattr(cms_lead, 'when_looking_to_start', None),
                    "ethnic_background": getattr(cms_lead, 'ethnic_background', None),
                    "eoi_nda_link": getattr(cms_lead, 'eoi_nda_link', None),
                    "work_background": getattr(cms_lead, 'work_background', None),
                    "motivation_to_enquire": getattr(cms_lead, 'motivation_to_enquire', None),
                    "funds_available": getattr(cms_lead, 'funds_available', None),
                    "motivation": getattr(cms_lead, 'motivation', None),

                    # Boolean fields
                    "have_run_business_before": getattr(cms_lead, 'have_run_business_before', None),
                    "have_mortgage": getattr(cms_lead, 'have_mortgage', None),
                    "high_net_worth": getattr(cms_lead, 'high_net_worth', None),

                    # System fields
                    "zoho_lead_id": cms_lead.zoho_lead_id,
                    "created_at": cms_lead.created_at,
                    "updated_at": cms_lead.updated_at
                }

                # Use the proper mapper to convert to Zoho format
                zoho_data = ZohoDataMapper.cms_lead_to_zoho(cms_lead_dict)
                batch_data.append(zoho_data)

                # Store mapping for later reference
                lead_mapping[len(batch_data) - 1] = cms_lead

            logger.info(f"Sending batch of {len(batch_data)} leads to Zoho")

            # Send batch to Zoho
            batch_results = await zoho_client.create_leads(batch_data)

            # Process results and update CMS leads with Zoho IDs
            for i, result in enumerate(batch_results):
                cms_lead = lead_mapping.get(i)
                if not cms_lead:
                    continue

                if result.get("status") == "success":
                    zoho_id = result.get("details", {}).get("id")
                    if zoho_id:
                        cms_lead.zoho_lead_id = zoho_id
                        results["leads_pushed"] += 1
                        lead_name = f"{cms_lead.first_name} {cms_lead.last_name}".strip()
                        logger.info(f"Successfully pushed lead {lead_name} to Zoho with ID {zoho_id}")
                    else:
                        lead_name = f"{cms_lead.first_name} {cms_lead.last_name}".strip()
                        logger.error(f"No Zoho ID returned for lead {lead_name}")
                        results["errors"].append(f"No Zoho ID for lead {lead_name}")
                else:
                    error_msg = result.get("message", "Unknown error")
                    lead_name = f"{cms_lead.first_name} {cms_lead.last_name}".strip()
                    logger.error(f"Failed to create lead {lead_name} in Zoho: {error_msg}")
                    results["errors"].append(f"Failed to create lead {lead_name}: {error_msg}")

            logger.info(f"Batch processing completed. Successfully pushed {results['leads_pushed']} leads.")

        except Exception as e:
            logger.error(f"Failed to push leads to Zoho: {e}")
            results["errors"].append(f"Push failed: {e}")

    async def _push_updated_leads_to_zoho(self, results: Dict) -> None:
        """Push updated CMS leads to Zoho using batch update operations"""
        try:
            # Get CMS leads that have Zoho ID and were updated recently
            # We'll use a simple heuristic: leads updated in the last hour
            from datetime import datetime, timedelta
            cutoff_time = datetime.now() - timedelta(hours=1)

            stmt = select(Lead).options(
                selectinload(Lead.lead_source_rel),
                selectinload(Lead.lead_status_rel)
            ).where(
                and_(
                    Lead.zoho_lead_id.isnot(None),  # Has Zoho ID (already synced)
                    Lead.updated_at > cutoff_time,  # Updated recently
                    Lead.is_deleted == False,
                    Lead.is_active == True
                )
            )
            result = await self.db.execute(stmt)
            updated_leads = result.scalars().all()

            if not updated_leads:
                logger.info("No updated leads to push to Zoho")
                return

            logger.info(f"Found {len(updated_leads)} updated CMS leads to push to Zoho")

            # Prepare batch update data
            batch_data = []
            lead_mapping = {}  # To map batch results back to CMS leads

            for cms_lead in updated_leads:
                # Convert CMS lead to dictionary format
                cms_lead_dict = {
                    # Basic information
                    "first_name": cms_lead.first_name,
                    "last_name": cms_lead.last_name,
                    "email": cms_lead.email,
                    "phone": cms_lead.phone,
                    "mobile": cms_lead.mobile,
                    "location": cms_lead.location,
                    "postal_code": cms_lead.postal_code,
                    "brand_preference": cms_lead.brand_preference,

                    # Lead source and status relationships (get from related objects)
                    "lead_source": getattr(cms_lead.lead_source_rel, 'name', None) if cms_lead.lead_source_rel else None,
                    "status": getattr(cms_lead.lead_status_rel, 'name', None) if cms_lead.lead_status_rel else None,

                    # System fields
                    "zoho_lead_id": cms_lead.zoho_lead_id,
                    "updated_at": cms_lead.updated_at
                }

                # Use the proper mapper to convert to Zoho format
                from app.services.zoho_mapper import ZohoDataMapper
                zoho_data = ZohoDataMapper.cms_lead_to_zoho(cms_lead_dict)

                # Add the Zoho ID for update operation
                zoho_data["id"] = cms_lead.zoho_lead_id

                batch_data.append(zoho_data)
                lead_mapping[len(batch_data) - 1] = cms_lead

            logger.info(f"Sending batch update of {len(batch_data)} leads to Zoho")

            # Send batch update to Zoho
            batch_results = await zoho_client.update_leads(batch_data)

            # Process results
            for i, result in enumerate(batch_results):
                cms_lead = lead_mapping.get(i)
                if not cms_lead:
                    continue

                if result.get("status") == "success":
                    results["leads_updated"] += 1
                    lead_name = f"{cms_lead.first_name} {cms_lead.last_name}".strip()
                    logger.info(f"Successfully updated lead {lead_name} in Zoho")
                else:
                    error_msg = result.get("message", "Unknown error")
                    lead_name = f"{cms_lead.first_name} {cms_lead.last_name}".strip()
                    logger.error(f"Failed to update lead {lead_name} in Zoho: {error_msg}")
                    results["errors"].append(f"Failed to update lead {lead_name}: {error_msg}")

            logger.info(f"Batch update completed. Successfully updated {results.get('leads_updated', 0)} leads.")

        except Exception as e:
            logger.error(f"Failed to push updated leads to Zoho: {e}")
            results["errors"].append(f"Update push failed: {e}")

    async def _should_update_from_zoho(self, cms_lead: Lead, zoho_lead: Dict) -> bool:
        """Check if CMS lead should be updated from Zoho (conflict resolution)"""
        try:
            # Parse Zoho modified time
            zoho_modified_str = zoho_lead.get("Modified_Time", "")
            if not zoho_modified_str:
                return False

            # Use the mapper to parse the datetime properly
            from app.services.zoho_mapper import ZohoDataMapper
            zoho_modified = ZohoDataMapper._parse_zoho_datetime(zoho_modified_str)
            cms_modified = cms_lead.updated_at

            # Give priority to latest timestamp
            return zoho_modified > cms_modified

        except Exception as e:
            logger.error(f"Error comparing timestamps: {e}")
            return False
    
    async def _create_lead_from_zoho(self, zoho_lead: Dict) -> None:
        """Create new CMS lead from Zoho data"""
        try:
            # Use the proper mapper to convert from Zoho format
            from app.services.zoho_mapper import ZohoDataMapper

            # DEBUG: Log the raw Zoho lead data
            logger.info(f"DEBUG: Raw Zoho lead data for {zoho_lead.get('First_Name', '')} {zoho_lead.get('Last_Name', '')}: {zoho_lead}")

            # DEBUG: Specifically check postal code fields in raw Zoho data
            zip_code_value = zoho_lead.get("Zip_Code")
            postcode_value = zoho_lead.get("Postcode")
            logger.info(f"DEBUG: Raw Zoho Zip_Code: '{zip_code_value}' (type: {type(zip_code_value)})")
            logger.info(f"DEBUG: Raw Zoho Postcode: '{postcode_value}' (type: {type(postcode_value)})")

            cms_lead_dict = ZohoDataMapper.zoho_lead_to_cms(zoho_lead)

            # DEBUG: Log the mapped CMS lead data
            logger.info(f"DEBUG: Mapped CMS lead data: {cms_lead_dict}")

            # DEBUG: Specifically check postal code
            postal_code_value = cms_lead_dict.get("postal_code")
            logger.info(f"DEBUG: Postal code value: '{postal_code_value}' (type: {type(postal_code_value)})")

            # Handle lead source - we'll need to look up or create the lead source
            if cms_lead_dict.get("lead_source"):
                lead_source_id = await self._get_or_create_lead_source(cms_lead_dict["lead_source"])
                if lead_source_id:
                    cms_lead_dict["lead_source_id"] = lead_source_id
                # Remove the string field since we have the ID
                cms_lead_dict.pop("lead_source", None)

            # Handle lead status - we'll need to look up or create the lead status
            if cms_lead_dict.get("status"):
                lead_status_id = await self._get_or_create_lead_status(cms_lead_dict["status"])
                if lead_status_id:
                    cms_lead_dict["lead_status_id"] = lead_status_id
                # Remove the string field since we have the ID
                cms_lead_dict.pop("status", None)
            else:
                # Use default lead status if none provided
                lead_status_id = await self._get_or_create_lead_status("New Lead")
                if lead_status_id:
                    cms_lead_dict["lead_status_id"] = lead_status_id

            # Handle franchisor relationship - prioritize franchisor_won_id over name
            franchisor_id = None
            logger.info(f"DEBUG: Processing franchisor relationship - won_id: {cms_lead_dict.get('franchisor_won_id')}, name: {cms_lead_dict.get('franchisor_name')}")

            if cms_lead_dict.get("franchisor_won_id"):
                # First try to find by franchisor_won_id (more accurate)
                logger.info(f"DEBUG: Searching for franchisor by won_id: {cms_lead_dict['franchisor_won_id']}")
                franchisor_id = await self._find_franchisor_by_won_id(cms_lead_dict["franchisor_won_id"])
                if franchisor_id:
                    logger.info(f"✅ Found franchisor by won_id {cms_lead_dict['franchisor_won_id']}: {franchisor_id}")
                else:
                    logger.warning(f"❌ No franchisor found for won_id: {cms_lead_dict['franchisor_won_id']}")

            # Fallback to name-based search if won_id didn't work
            if not franchisor_id and cms_lead_dict.get("franchisor_name"):
                logger.info(f"DEBUG: Searching for franchisor by name: {cms_lead_dict['franchisor_name']}")
                franchisor_id = await self._find_franchisor_by_name(cms_lead_dict["franchisor_name"])
                if franchisor_id:
                    logger.info(f"✅ Found franchisor by name {cms_lead_dict['franchisor_name']}: {franchisor_id}")
                else:
                    logger.warning(f"❌ No franchisor found for name: {cms_lead_dict['franchisor_name']}")

            if franchisor_id:
                cms_lead_dict["brand_preference"] = franchisor_id
                logger.info(f"DEBUG: Set brand_preference to: {franchisor_id}")
            else:
                logger.warning(f"DEBUG: No franchisor found - brand_preference will be None")

            # Remove the string fields since we have the ID (or tried to get it)
            cms_lead_dict.pop("franchisor_name", None)
            cms_lead_dict.pop("franchisor_won_id", None)

            # Set timestamps only if not already set by mapper
            if not cms_lead_dict.get("created_at"):
                cms_lead_dict["created_at"] = datetime.now(datetime.timezone.utc)
            if not cms_lead_dict.get("updated_at"):
                cms_lead_dict["updated_at"] = datetime.now(datetime.timezone.utc)

            # Remove None values and fields that don't exist in the Lead model
            valid_lead_fields = {
                'zoho_lead_id', 'first_name', 'last_name', 'phone', 'mobile', 'email',
                'location', 'postal_code', 'lead_source_id', 'lead_status_id',
                'brand_preference', 'budget_preference', 'franchise_interested_in',
                'looking_for_business_opportunity_since', 'skills', 'looking_to_be_owner_operator',
                'when_looking_to_start', 'ethnic_background', 'funds_to_invest', 'eoi_nda_link',
                'work_background', 'motivation_to_enquire', 'funds_available', 'motivation',
                'have_run_business_before', 'have_mortgage', 'high_net_worth', 'created_at', 'updated_at'
            }

            # Allow postal_code to be empty string (some leads might not have postal codes)
            lead_data = {}
            for k, v in cms_lead_dict.items():
                if k in valid_lead_fields:
                    if k == "postal_code":
                        # Allow postal_code even if empty, but not None
                        if v is not None:
                            lead_data[k] = v
                    else:
                        # Standard filtering for other fields
                        if v is not None and v != "":
                            lead_data[k] = v

            # DEBUG: Check if postal_code was filtered out
            if "postal_code" in cms_lead_dict and "postal_code" not in lead_data:
                logger.warning(f"DEBUG: postal_code was filtered out! Original value: '{cms_lead_dict['postal_code']}'")
            elif "postal_code" in lead_data:
                logger.info(f"DEBUG: postal_code included in final data: '{lead_data['postal_code']}'")
            else:
                logger.warning(f"DEBUG: postal_code not in original mapped data")

            # DEBUG: Log the final lead data being used to create the Lead
            logger.info(f"DEBUG: Final lead data for database: {lead_data}")

            new_lead = Lead(**lead_data)
            self.db.add(new_lead)

            # DEBUG: Log what postal_code was set on the new lead
            if hasattr(new_lead, 'postal_code'):
                logger.info(f"DEBUG: New lead created with postal_code: '{new_lead.postal_code}'")

            # DEBUG: Before commit, check all lead attributes
            logger.info(f"DEBUG: Lead attributes before commit:")
            for attr in ['first_name', 'last_name', 'email', 'phone', 'mobile', 'postal_code']:
                if hasattr(new_lead, attr):
                    value = getattr(new_lead, attr)
                    logger.info(f"  {attr}: '{value}'")

        except Exception as e:
            logger.error(f"Failed to create lead from Zoho: {e}")
            raise
    
    async def _update_lead_from_zoho(self, cms_lead: Lead, zoho_lead: Dict) -> None:
        """Update existing CMS lead with Zoho data"""
        try:
            # Use the proper mapper to convert from Zoho format
            from app.services.zoho_mapper import ZohoDataMapper
            cms_lead_dict = ZohoDataMapper.zoho_lead_to_cms(zoho_lead)

            # Update all fields from the mapped data
            for key, value in cms_lead_dict.items():
                if hasattr(cms_lead, key):
                    # Skip system fields that shouldn't be updated
                    if key not in ['id', 'created_at', 'zoho_lead_id']:
                        # Special handling for postal_code - allow empty strings
                        if key == "postal_code":
                            if value is not None:
                                setattr(cms_lead, key, value)
                                logger.info(f"DEBUG: Updated existing lead postal_code to: '{value}'")
                        else:
                            # Standard filtering for other fields
                            if value is not None and value != "":
                                setattr(cms_lead, key, value)

            # Handle lead source relationship
            if cms_lead_dict.get("lead_source"):
                lead_source_id = await self._get_or_create_lead_source(cms_lead_dict["lead_source"])
                if lead_source_id:
                    cms_lead.lead_source_id = lead_source_id

            # Handle lead status relationship
            if cms_lead_dict.get("status"):
                lead_status_id = await self._get_or_create_lead_status(cms_lead_dict["status"])
                if lead_status_id:
                    cms_lead.lead_status_id = lead_status_id

            # Handle franchisor relationship - prioritize franchisor_won_id over name
            franchisor_id = None
            if cms_lead_dict.get("franchisor_won_id"):
                # First try to find by franchisor_won_id (more accurate)
                franchisor_id = await self._find_franchisor_by_won_id(cms_lead_dict["franchisor_won_id"])
                if franchisor_id:
                    logger.info(f"Found franchisor by won_id {cms_lead_dict['franchisor_won_id']}: {franchisor_id}")
                else:
                    logger.warning(f"No franchisor found for won_id: {cms_lead_dict['franchisor_won_id']}")

            # Fallback to name-based search if won_id didn't work
            if not franchisor_id and cms_lead_dict.get("franchisor_name"):
                franchisor_id = await self._find_franchisor_by_name(cms_lead_dict["franchisor_name"])
                if franchisor_id:
                    logger.info(f"Found franchisor by name {cms_lead_dict['franchisor_name']}: {franchisor_id}")

            if franchisor_id:
                cms_lead.brand_preference = franchisor_id

            # Note: updated_at is already set from the mapper with the Zoho timestamp
            # Don't override it with current time

        except Exception as e:
            logger.error(f"Failed to update lead from Zoho: {e}")
            raise
    
    async def _push_lead_to_zoho(self, cms_lead: Lead) -> Optional[str]:
        """Push CMS lead to Zoho and return Zoho ID"""
        try:
            # Combine first and last name for full name
            full_name = f"{cms_lead.first_name} {cms_lead.last_name}".strip()
            if not full_name:
                full_name = cms_lead.first_name or "Unknown"

            # Get lead source and status names for mapping
            lead_source_name = None
            if cms_lead.lead_source_rel:
                lead_source_name = cms_lead.lead_source_rel.name

            lead_status_name = None
            if cms_lead.lead_status_rel:
                lead_status_name = cms_lead.lead_status_rel.name

            # Convert Lead model to dict for mapper
            cms_lead_dict = {
                "full_name": full_name,
                "email": cms_lead.email,
                "phone": cms_lead.phone,
                "mobile": cms_lead.mobile,
                "lead_source": lead_source_name,
                "status": lead_status_name,
                "location": cms_lead.location,
                "budget_preference": getattr(cms_lead, 'budget_preference', None),
                "brand_preference": getattr(cms_lead, 'brand_preference', None),
                "qualification_status": getattr(cms_lead, 'qualification_status', None),
                "zoho_lead_id": cms_lead.zoho_lead_id
            }

            # Get franchisor name if brand_preference is set
            if cms_lead.brand_preference:
                franchisor_name = await self._get_franchisor_name_by_id(cms_lead.brand_preference)
                if franchisor_name:
                    cms_lead_dict["franchisor_name"] = franchisor_name

            # Use the proper mapper to convert to Zoho format
            from app.services.zoho_mapper import ZohoDataMapper
            zoho_data = ZohoDataMapper.cms_lead_to_zoho(cms_lead_dict)

            # Debug: Log the data being sent to Zoho
            logger.info(f"Sending data to Zoho for lead {cms_lead.id}: {zoho_data}")

            # Create lead in Zoho
            response = await zoho_client.create_lead(zoho_data)

            if response and response.get("data") and len(response["data"]) > 0:
                return response["data"][0].get("details", {}).get("id")

        except Exception as e:
            logger.error(f"Failed to push lead {cms_lead.id} to Zoho: {e}")

        return None
    

    async def get_sync_status(self) -> Dict[str, any]:
        """Get current sync status"""
        try:
            # Count leads with/without Zoho IDs
            stmt_synced = select(Lead).where(Lead.zoho_lead_id.isnot(None))
            stmt_unsynced = select(Lead).where(Lead.zoho_lead_id.is_(None))
            
            result_synced = await self.db.execute(stmt_synced)
            result_unsynced = await self.db.execute(stmt_unsynced)
            
            synced_count = len(result_synced.scalars().all())
            unsynced_count = len(result_unsynced.scalars().all())
            
            return {
                "success": True,
                "data": {
                    "synced_leads": synced_count,
                    "unsynced_leads": unsynced_count,
                    "total_leads": synced_count + unsynced_count
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get sync status: {e}")
            return {"success": False, "message": f"Failed to get sync status: {e}"}

    async def _pull_franchisors_from_zoho(self, results: Dict) -> None:
        """Pull new and updated franchisors from Zoho - only those with 'Sale Won - 100%' status"""
        try:
            # Get all franchisors from Zoho
            all_zoho_franchisors = await zoho_client.get_franchisor()
            logger.info(f"Retrieved {len(all_zoho_franchisors)} total franchisors from Zoho")

            # DEBUG: Log all franchisor sales stages
            logger.info("DEBUG: All franchisor sales stages from Zoho:")
            for i, franchisor in enumerate(all_zoho_franchisors[:10]):  # Log first 10
                name = franchisor.get("Name", "N/A")
                sales_stage = franchisor.get("Sales_Stage", "N/A")
                franchisor_won_id = franchisor.get("Franchisor_Won_ID", "N/A")
                modified_time = franchisor.get("Modified_Time", "N/A")
                logger.info(f"  {i+1}. {name} | Stage: '{sales_stage}' | Won_ID: {franchisor_won_id} | Modified: {modified_time}")

            # Filter only franchisors with "Sale Won - 100%" status
            zoho_franchisors = [
                franchisor for franchisor in all_zoho_franchisors
                if franchisor.get("Sales_Stage") and franchisor.get("Sales_Stage", "").lower() == "sale won - 100%"
            ]
            logger.info(f"Filtered to {len(zoho_franchisors)} franchisors with 'Sale Won - 100%' status")

            # DEBUG: Log filtered franchisors
            if zoho_franchisors:
                logger.info("DEBUG: Franchisors with 'Sale Won - 100%' status:")
                for i, franchisor in enumerate(zoho_franchisors):
                    name = franchisor.get("Name", "N/A")
                    franchisor_won_id = franchisor.get("Franchisor_Won_ID", "N/A")
                    modified_time = franchisor.get("Modified_Time", "N/A")
                    logger.info(f"  {i+1}. {name} | Won_ID: {franchisor_won_id} | Modified: {modified_time}")
            else:
                logger.warning("DEBUG: No franchisors found with 'Sale Won - 100%' status!")

            for zoho_franchisor in zoho_franchisors:
                zoho_id = zoho_franchisor.get("id")
                if not zoho_id:
                    continue

                # Use the proper mapper to convert from Zoho format
                from app.services.zoho_mapper import ZohoDataMapper
                cms_franchisor_dict = ZohoDataMapper.zoho_franchisor_to_cms(zoho_franchisor)

                # DEBUG: Log the conversion
                logger.info(f"DEBUG: Processing Zoho franchisor {zoho_id}")
                logger.info(f"  Name: {zoho_franchisor.get('Name', 'N/A')}")
                logger.info(f"  Sales_Stage: {zoho_franchisor.get('Sales_Stage', 'N/A')}")
                logger.info(f"  Franchisor_Won_ID: {zoho_franchisor.get('Franchisor_Won_ID', 'N/A')}")
                logger.info(f"  Modified_Time: {zoho_franchisor.get('Modified_Time', 'N/A')}")

                # Handle industry mapping
                if zoho_franchisor.get("Industry"):
                    industry_id = await self._get_or_create_industry(zoho_franchisor["Industry"])
                    if industry_id:
                        cms_franchisor_dict["industry_id"] = industry_id

                # Check if franchisor exists in CMS by franchisor_won_id OR zoho_franchisor_id
                franchisor_won_id = cms_franchisor_dict.get("franchisor_won_id")
                zoho_franchisor_id = cms_franchisor_dict.get("zoho_franchisor_id")

                if not franchisor_won_id and not zoho_franchisor_id:
                    logger.warning(f"DEBUG: Zoho franchisor {zoho_id} has no franchisor_won_id or zoho_franchisor_id, skipping")
                    continue

                logger.info(f"DEBUG: Looking for CMS franchisor with franchisor_won_id: {franchisor_won_id} or zoho_franchisor_id: {zoho_franchisor_id}")

                # Check by both franchisor_won_id and zoho_franchisor_id to prevent duplicates
                conditions = []
                if franchisor_won_id:
                    conditions.append(Franchisor.franchisor_won_id == franchisor_won_id)
                if zoho_franchisor_id:
                    conditions.append(Franchisor.zoho_franchisor_id == zoho_franchisor_id)

                stmt = select(Franchisor).where(or_(*conditions))
                result = await self.db.execute(stmt)
                existing_franchisor = result.scalar_one_or_none()

                if existing_franchisor:
                    logger.info(f"DEBUG: Found existing CMS franchisor: {existing_franchisor.name}")
                    logger.info(f"  CMS updated_at: {existing_franchisor.updated_at}")

                    # Check if Zoho version is newer using the updated_at from CMS dict (parsed from Zoho Modified_Time)
                    zoho_updated_at = cms_franchisor_dict.get("updated_at")
                    logger.info(f"  Zoho updated_at: {zoho_updated_at}")

                    if zoho_updated_at and existing_franchisor.updated_at < zoho_updated_at:
                        logger.info(f"DEBUG: Zoho version is newer, updating franchisor")
                        # Update existing franchisor with Zoho data
                        for key, value in cms_franchisor_dict.items():
                            if hasattr(existing_franchisor, key) and key not in ['id', 'created_at']:
                                old_value = getattr(existing_franchisor, key)
                                if old_value != value:
                                    logger.info(f"    Updating {key}: '{old_value}' -> '{value}'")
                                setattr(existing_franchisor, key, value)

                        existing_franchisor.updated_at = datetime.utcnow()
                        results["franchisors_updated"] += 1
                        logger.info(f"✅ Updated existing franchisor from Zoho ID {zoho_id}")
                    else:
                        logger.info(f"DEBUG: Franchisor {zoho_id} already exists and is up to date (Zoho not newer)")
                        logger.info(f"  Reason: zoho_updated={zoho_updated_at}, cms_updated={existing_franchisor.updated_at}")
                else:
                    logger.info(f"DEBUG: No existing CMS franchisor found, creating new one")
                    try:
                        # Create new franchisor from Zoho data
                        new_franchisor = Franchisor(**cms_franchisor_dict)
                        self.db.add(new_franchisor)
                        results["franchisors_pulled"] += 1
                        logger.info(f"✅ Created new franchisor from Zoho ID {zoho_id} with name: {cms_franchisor_dict.get('name', 'N/A')}")
                    except Exception as create_error:
                        logger.error(f"Failed to create franchisor from Zoho ID {zoho_id}: {create_error}")
                        await self.db.rollback()
                        # Skip this franchisor and continue with others
                        continue

        except Exception as e:
            logger.error(f"Error pulling franchisors from Zoho: {e}")
            results["errors"].append(f"Pull franchisors error: {e}")

    async def _push_franchisors_to_zoho(self, results: Dict) -> None:
        """Push new and updated franchisors to Zoho using batch operations"""
        try:
            # Find CMS franchisors that don't have zoho_franchisor_id (new ones) with industry relationship
            stmt = select(Franchisor).options(
                selectinload(Franchisor.industry_rel)
            ).where(
                and_(
                    Franchisor.zoho_franchisor_id.is_(None),
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            cms_franchisors = result.scalars().all()

            if not cms_franchisors:
                logger.info("No new franchisors to push to Zoho")
                return

            logger.info(f"Found {len(cms_franchisors)} CMS franchisors to push to Zoho")

            # Get existing Zoho franchisors to check for duplicates by name
            try:
                existing_zoho_franchisors = await zoho_client.get_franchisor()
                existing_names = {f.get("Name", "").lower() for f in existing_zoho_franchisors if f.get("Name")}
                logger.info(f"Found {len(existing_names)} existing franchisor names in Zoho")
            except Exception as e:
                logger.warning(f"Could not fetch existing Zoho franchisors: {e}")
                existing_names = set()

            # Prepare batch data, skipping duplicates
            batch_data = []
            franchisor_mapping = {}  # To map batch results back to CMS franchisors
            skipped_duplicates = 0

            for cms_franchisor in cms_franchisors:
                # Check if franchisor name already exists in Zoho
                if cms_franchisor.name.lower() in existing_names:
                    logger.info(f"Skipping franchisor '{cms_franchisor.name}' - already exists in Zoho")
                    skipped_duplicates += 1
                    continue
                # Get industry name for mapping
                industry_name = None
                if cms_franchisor.industry_rel:
                    industry_name = cms_franchisor.industry_rel.name

                # Convert to dict for mapper
                cms_franchisor_dict = {
                    "id": str(cms_franchisor.id),
                    "name": cms_franchisor.name,
                    "contactfirstname": cms_franchisor.contactfirstname,
                    "contactlastname": cms_franchisor.contactlastname,
                    "email": cms_franchisor.email,
                    "phone": cms_franchisor.phone,
                    "region": cms_franchisor.region,
                    "budget": cms_franchisor.budget,
                    "industry": industry_name,  # Add industry name for Zoho mapping
                    "is_active": cms_franchisor.is_active,
                    "created_at": cms_franchisor.created_at,
                    "updated_at": cms_franchisor.updated_at
                }

                # Use the proper mapper to convert to Zoho format
                zoho_data = ZohoDataMapper.cms_franchisor_to_zoho(cms_franchisor_dict)
                batch_data.append(zoho_data)

                # Store mapping for later reference
                franchisor_mapping[len(batch_data) - 1] = cms_franchisor

            if skipped_duplicates > 0:
                logger.info(f"Skipped {skipped_duplicates} franchisors that already exist in Zoho")

            if not batch_data:
                logger.info("No new franchisors to push to Zoho (all were duplicates or already synced)")
                return

            logger.info(f"Sending batch of {len(batch_data)} franchisors to Zoho")

            # Send batch to Zoho
            batch_results = await zoho_client.create_franchisor_batch(batch_data)

            # Process results and update CMS franchisors with Zoho IDs
            for i, result in enumerate(batch_results):
                cms_franchisor = franchisor_mapping.get(i)
                if not cms_franchisor:
                    continue

                if result.get("status") == "success":
                    zoho_id = result.get("details", {}).get("id")
                    if zoho_id:
                        cms_franchisor.zoho_franchisor_id = zoho_id
                        results["franchisors_pushed"] += 1
                        logger.info(f"Successfully pushed franchisor {cms_franchisor.name} to Zoho with ID {zoho_id}")
                    else:
                        logger.error(f"No Zoho ID returned for franchisor {cms_franchisor.name}")
                        results["errors"].append(f"No Zoho ID for franchisor {cms_franchisor.name}")
                elif result.get("code") == "DUPLICATE_DATA":
                    # This shouldn't happen now since we pre-check for duplicates
                    zoho_id = result.get("details", {}).get("id")
                    logger.warning(f"Unexpected duplicate data for franchisor {cms_franchisor.name}, Zoho ID: {zoho_id}")
                    if zoho_id:
                        # Just store the Zoho ID without updating
                        cms_franchisor.zoho_franchisor_id = zoho_id
                        results["franchisors_updated"] += 1
                        logger.info(f"Linked existing franchisor {cms_franchisor.name} to Zoho ID {zoho_id}")
                    else:
                        results["errors"].append(f"Duplicate franchisor {cms_franchisor.name} but no Zoho ID provided")
                else:
                    error_msg = result.get("message", "Unknown error")
                    logger.error(f"Failed to create franchisor {cms_franchisor.name} in Zoho: {error_msg}")
                    results["errors"].append(f"Failed to create franchisor {cms_franchisor.name}: {error_msg}")

            logger.info(f"Batch processing completed. Successfully pushed {results['franchisors_pushed']} franchisors.")

        except Exception as e:
            logger.error(f"Error pushing franchisors to Zoho: {e}")
            results["errors"].append(f"Push franchisors error: {e}")

    async def _find_franchisor_by_name(self, franchisor_name: str) -> Optional[str]:
        """Find franchisor ID by name"""
        try:
            logger.info(f"DEBUG: Searching for franchisor with name containing: '{franchisor_name}'")

            stmt = select(Franchisor).where(
                and_(
                    Franchisor.name.ilike(f"%{franchisor_name}%"),
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            franchisor = result.scalar_one_or_none()

            if franchisor:
                logger.info(f"DEBUG: Found matching franchisor by name: {franchisor.name} (ID: {franchisor.id})")
                return str(franchisor.id)
            else:
                logger.warning(f"DEBUG: No franchisor found for name: '{franchisor_name}'")
                return None

        except Exception as e:
            logger.error(f"Error finding franchisor by name '{franchisor_name}': {e}")
            return None

    async def _find_franchisor_by_won_id(self, franchisor_won_id: str) -> Optional[str]:
        """Find franchisor ID by franchisor_won_id (from Zoho)"""
        try:
            if not franchisor_won_id:
                logger.info(f"DEBUG: franchisor_won_id is empty or None")
                return None

            logger.info(f"DEBUG: Searching for franchisor with won_id: '{franchisor_won_id}'")

            # First, let's see what franchisors exist in the database
            all_stmt = select(Franchisor).where(
                and_(
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
            )
            all_result = await self.db.execute(all_stmt)
            all_franchisors = all_result.scalars().all()

            logger.info(f"DEBUG: Found {len(all_franchisors)} active franchisors in database:")
            for f in all_franchisors:
                logger.info(f"  - ID: {f.id}, Name: {f.name}, Won_ID: {f.franchisor_won_id}")

            stmt = select(Franchisor).where(
                and_(
                    Franchisor.franchisor_won_id == franchisor_won_id,
                    Franchisor.is_active == True,
                    Franchisor.is_deleted == False
                )
            )
            result = await self.db.execute(stmt)
            franchisor = result.scalar_one_or_none()

            if franchisor:
                logger.info(f"DEBUG: Found matching franchisor: {franchisor.name} (ID: {franchisor.id})")
                return str(franchisor.id)
            else:
                logger.warning(f"DEBUG: No franchisor found for won_id: '{franchisor_won_id}'")
                logger.warning(f"DEBUG: Available won_ids in database: {[f.franchisor_won_id for f in all_franchisors if f.franchisor_won_id]}")
                return None

        except Exception as e:
            logger.error(f"Error finding franchisor by won_id '{franchisor_won_id}': {e}")
            return None

    async def _get_franchisor_name_by_id(self, franchisor_id: str) -> Optional[str]:
        """Get franchisor name by ID"""
        try:
            stmt = select(Franchisor).where(Franchisor.id == franchisor_id)
            result = await self.db.execute(stmt)
            franchisor = result.scalar_one_or_none()

            if franchisor:
                return franchisor.name
            else:
                logger.warning(f"Franchisor not found for ID: {franchisor_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting franchisor name for ID '{franchisor_id}': {e}")
            return None

    async def _get_or_create_lead_source(self, source_name: str) -> Optional[str]:
        """Get or create lead source by name"""
        try:
            from app.models.lead_reference import LeadSource

            # Try to find existing lead source
            stmt = select(LeadSource).where(LeadSource.name == source_name)
            result = await self.db.execute(stmt)
            lead_source = result.scalar_one_or_none()

            if lead_source:
                return str(lead_source.id)

            # Create new lead source if not found
            new_source = LeadSource(
                name=source_name,
                is_active=True
            )
            self.db.add(new_source)
            await self.db.commit()
            await self.db.refresh(new_source)

            logger.info(f"Created new lead source: {source_name}")
            return str(new_source.id)

        except Exception as e:
            logger.error(f"Error getting/creating lead source '{source_name}': {e}")
            return None

    async def _get_or_create_lead_status(self, status_name: str) -> Optional[str]:
        """Get or create lead status by name"""
        try:
            from app.models.lead_reference import LeadStatus

            # Try to find existing lead status
            stmt = select(LeadStatus).where(LeadStatus.name == status_name)
            result = await self.db.execute(stmt)
            lead_status = result.scalar_one_or_none()

            if lead_status:
                return str(lead_status.id)

            # Create new lead status if not found
            new_status = LeadStatus(
                name=status_name,
                colour="#808080",  # Default gray color
                is_active=True
            )
            self.db.add(new_status)
            await self.db.commit()
            await self.db.refresh(new_status)

            logger.info(f"Created new lead status: {status_name}")
            return str(new_status.id)

        except Exception as e:
            logger.error(f"Error getting/creating lead status '{status_name}': {e}")
            return None

    async def _get_or_create_industry(self, industry_name: str) -> Optional[str]:
        """Get or create industry by name"""
        try:
            from app.models.industry import Industry

            # Try to find existing industry
            stmt = select(Industry).where(Industry.name == industry_name)
            result = await self.db.execute(stmt)
            industry = result.scalar_one_or_none()

            if industry:
                return str(industry.id)

            # Create new industry if not found
            new_industry = Industry(
                name=industry_name,
                is_active=True
            )
            self.db.add(new_industry)
            await self.db.commit()
            await self.db.refresh(new_industry)

            logger.info(f"Created new industry: {industry_name}")
            return str(new_industry.id)

        except Exception as e:
            logger.error(f"Error getting/creating industry '{industry_name}': {e}")
            return None


def get_simple_zoho_sync(db: AsyncSession) -> SimpleZohoSync:
    """Dependency injection for SimpleZohoSync"""
    return SimpleZohoSync(db)
