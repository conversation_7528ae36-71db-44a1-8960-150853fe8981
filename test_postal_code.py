#!/usr/bin/env python3
"""
Test script to debug postal code sync issue
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.database import get_db
    from app.models.lead import Lead
    from app.services.zoho_client import zoho_client
    from app.services.zoho_mapper import ZohoDataMapper
    from sqlalchemy import select
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

async def test_postal_code_mapping():
    """Test postal code mapping step by step"""
    print("🔍 Testing Postal Code Mapping")
    print("=" * 50)
    
    try:
        # Step 1: Get a sample lead from Zoho
        print("\n📥 Step 1: Getting sample lead from Zoho...")
        zoho_leads = await zoho_client.get_leads()
        if not zoho_leads:
            print("❌ No leads found in Zoho")
            return
            
        sample_lead = zoho_leads[0]
        print(f"✅ Got sample lead: {sample_lead.get('First_Name', '')} {sample_lead.get('Last_Name', '')}")
        
        # Step 2: Check raw postal code fields
        print("\n📋 Step 2: Checking raw postal code fields...")
        zip_code = sample_lead.get("Zip_Code")
        postcode = sample_lead.get("Postcode")
        print(f"  Zip_Code: '{zip_code}' (type: {type(zip_code)})")
        print(f"  Postcode: '{postcode}' (type: {type(postcode)})")
        
        # Step 3: Test the mapping
        print("\n🔄 Step 3: Testing ZohoDataMapper...")
        cms_lead_dict = ZohoDataMapper.zoho_lead_to_cms(sample_lead)
        mapped_postal_code = cms_lead_dict.get("postal_code")
        print(f"  Mapped postal_code: '{mapped_postal_code}' (type: {type(mapped_postal_code)})")
        
        # Step 4: Test the filtering logic
        print("\n🔍 Step 4: Testing filtering logic...")
        valid_lead_fields = {
            'zoho_lead_id', 'first_name', 'last_name', 'phone', 'mobile', 'email',
            'location', 'postal_code', 'lead_source_id', 'lead_status_id',
            'brand_preference', 'budget_preference', 'franchise_interested_in',
            'looking_for_business_opportunity_since', 'skills', 'looking_to_be_owner_operator',
            'when_looking_to_start', 'ethnic_background', 'funds_to_invest', 'eoi_nda_link',
            'work_background', 'motivation_to_enquire', 'funds_available', 'motivation',
            'have_run_business_before', 'have_mortgage', 'high_net_worth', 'created_at', 'updated_at'
        }
        
        # Simulate the filtering logic
        lead_data = {}
        for k, v in cms_lead_dict.items():
            if k in valid_lead_fields:
                if k == "postal_code":
                    # Allow postal_code even if empty, but not None
                    if v is not None:
                        lead_data[k] = v
                        print(f"  ✅ postal_code included: '{v}'")
                    else:
                        print(f"  ❌ postal_code excluded (None)")
                else:
                    # Standard filtering for other fields
                    if v is not None and v != "":
                        lead_data[k] = v
        
        print(f"  Final postal_code in lead_data: '{lead_data.get('postal_code', 'NOT_PRESENT')}'")
        
        # Step 5: Check database leads
        print("\n💾 Step 5: Checking existing leads in database...")
        async for db in get_db():
            try:
                stmt = select(Lead).order_by(Lead.created_at.desc()).limit(5)
                result = await db.execute(stmt)
                recent_leads = result.scalars().all()
                
                print(f"Found {len(recent_leads)} recent leads:")
                for lead in recent_leads:
                    print(f"  - {lead.first_name} {lead.last_name}: postal_code='{lead.postal_code}'")
                break
            except Exception as e:
                print(f"❌ Database error: {e}")
                break
        
        # Step 6: Test Lead model creation
        print("\n🏗️ Step 6: Testing Lead model creation...")
        try:
            test_lead_data = {
                'first_name': 'Test',
                'last_name': 'User',
                'email': '<EMAIL>',
                'postal_code': '7883'
            }
            
            test_lead = Lead(**test_lead_data)
            print(f"✅ Lead model created successfully")
            print(f"  postal_code attribute: '{test_lead.postal_code}'")
            
        except Exception as e:
            print(f"❌ Error creating Lead model: {e}")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_postal_code_mapping())
