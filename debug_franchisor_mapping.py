#!/usr/bin/env python3
"""
Debug script to check franchisor mapping between database and Zoho CRM
This will help identify why brand_preference is not being set properly
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.database import get_db
    from app.models.franchisor import Franchisor
    from app.services.zoho_client import zoho_client
    from sqlalchemy import select
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

async def check_franchisor_mapping():
    """Check franchisor mapping between database and Zoho"""
    print("🔍 Checking Franchisor Mapping Between Database and Zoho CRM")
    print("=" * 60)
    
    async for db in get_db():
        try:
            # Get franchisors from database
            print("\n📊 DATABASE FRANCHISORS:")
            stmt = select(Franchisor).where(Franchisor.is_deleted == False)
            result = await db.execute(stmt)
            db_franchisors = result.scalars().all()
            
            print(f"Found {len(db_franchisors)} franchisors in database:")
            for f in db_franchisors:
                print(f"  🏢 {f.name}")
                print(f"     - DB ID: {f.id}")
                print(f"     - Won ID: {f.franchisor_won_id}")
                print(f"     - Zoho ID: {f.zoho_franchisor_id}")
                print(f"     - Active: {f.is_active}")
                print()
            
            # Get franchisors from Zoho
            print("\n🌐 ZOHO CRM FRANCHISORS:")
            try:
                zoho_franchisors = await zoho_client.get_franchisor()
                print(f"Found {len(zoho_franchisors)} franchisors in Zoho:")
                
                for zf in zoho_franchisors:
                    print(f"  🏢 {zf.get('Name', 'N/A')}")
                    print(f"     - Zoho ID: {zf.get('id', 'N/A')}")
                    print(f"     - Won ID: {zf.get('Franchisor_Won_ID', 'N/A')}")
                    print(f"     - Sales Stage: {zf.get('Sales_Stage', 'N/A')}")
                    print()
                
            except Exception as e:
                print(f"❌ Error fetching Zoho franchisors: {e}")
                return
            
            # Check mapping issues
            print("\n🔍 MAPPING ANALYSIS:")
            print("-" * 40)
            
            # Create mapping dictionaries
            db_won_ids = {f.franchisor_won_id: f for f in db_franchisors if f.franchisor_won_id}
            zoho_won_ids = {zf.get('Franchisor_Won_ID'): zf for zf in zoho_franchisors if zf.get('Franchisor_Won_ID')}
            
            print(f"Database Won IDs: {list(db_won_ids.keys())}")
            print(f"Zoho Won IDs: {list(zoho_won_ids.keys())}")
            
            # Find mismatches
            missing_in_db = set(zoho_won_ids.keys()) - set(db_won_ids.keys())
            missing_in_zoho = set(db_won_ids.keys()) - set(zoho_won_ids.keys())
            matching = set(db_won_ids.keys()) & set(zoho_won_ids.keys())
            
            print(f"\n✅ Matching Won IDs: {list(matching)}")
            print(f"❌ Won IDs in Zoho but not in DB: {list(missing_in_db)}")
            print(f"❌ Won IDs in DB but not in Zoho: {list(missing_in_zoho)}")
            
            # Show specific lead example
            print("\n📋 SAMPLE LEAD ANALYSIS:")
            print("-" * 40)
            try:
                zoho_leads = await zoho_client.get_leads()
                if zoho_leads:
                    sample_lead = zoho_leads[0]
                    print(f"Sample Lead: {sample_lead.get('First_Name', '')} {sample_lead.get('Last_Name', '')}")
                    print(f"  - Franchisor: {sample_lead.get('Franchisor', 'N/A')}")
                    print(f"  - Franchisor_Id: {sample_lead.get('Franchisor_Id', 'N/A')}")
                    
                    franchisor_id = sample_lead.get('Franchisor_Id')
                    if franchisor_id:
                        if franchisor_id in db_won_ids:
                            print(f"  ✅ Franchisor mapping will work (found in DB)")
                        else:
                            print(f"  ❌ Franchisor mapping will fail (not found in DB)")
                            print(f"     Available DB Won IDs: {list(db_won_ids.keys())}")
            except Exception as e:
                print(f"❌ Error fetching sample lead: {e}")
            
            break
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            break

if __name__ == "__main__":
    asyncio.run(check_franchisor_mapping())
